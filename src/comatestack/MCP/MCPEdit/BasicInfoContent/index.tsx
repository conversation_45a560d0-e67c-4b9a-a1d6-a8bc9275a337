import styled from '@emotion/styled';
import {Form} from 'antd';
import {useCallback, useMemo} from 'react';
import {useWatch} from 'antd/es/form/Form';
import {MCPServerAuthType} from '@/types/mcp/mcp';
import EnglishIdentifierField from '../../MCPCreate/EnglishIdentifierField';
import GlobalVariableField from '../../MCPCreate/GlobalVariableField';
import MCPIdentifierField from '../../MCPCreate/MCPIdentifierField';
import ServiceNameField from '../../MCPCreate/ServiceNameField';
import AuthDescription from '../../MCPCreate/OpenApiFields/AuthDescriptionField';
import AuthTypeField from '../../MCPCreate/OpenApiFields/AuthTypeField';
import DescriptionField from './DescriptionField';
import SceneField from './SceneField';
import ProtocolField from './ProtocolField';
import OverviewField from './OverviewField';
import ServerConfigFieldWidthWrapper from './ServerConfigFieldWidthWrapper';

const ContentWrapper = styled.div`
    display: flex;
    justify-content: space-between;
    width: 100%;
    `;

const LeftContent = styled.div`
    height: calc(100vh - 240px);
    overflow-y: auto;
    padding-left: 24px;
    width: calc(55% - 24px);
    `;
const RightContent = styled.div`
    width: calc(45% - 24px);
    padding-right: 24px;
`;

/**
 * 如果path不以常量的形式定义在外部，每次控件渲染时都会收到一个新的数组引用。
 * 如果控件内部使用path作为effect的依赖项，会导致effect每次都执行。
 */
const SERVER_INFO_PATH = ['serverInfo'];
const SERVER_PARAMS_PATH = [...SERVER_INFO_PATH, 'serverParams'];
const SERVER_STATUS_PATH = [...SERVER_INFO_PATH, 'serverStatus'];

const BasicInfoContent = () => {
    const serverSourceType = Form.useWatch('serverSourceType');
    const form = Form.useFormInstance();
    const authType = useWatch(['serverInfo', 'serverConf', 'serverExtension', 'serverAuthType'], form);

    const systemVars = useMemo(
        () => {
            if (serverSourceType === 'openapi' && authType === 'CLOUD_INIT_IAM') {
                return [
                    {
                        name: 'AccessKey',
                        description: '请求时用于生成签名',
                        dataType: 'string',
                        required: true,
                        isSystemVar: true,
                    },
                    {
                        name: 'SecretKey',
                        description: '请求时用于生成签名',
                        dataType: 'string',
                        required: true,
                        isSystemVar: true,
                    },
                ];
            }
            return [];
        },
        [serverSourceType, authType]
    );

    const handleAuthTypeChange = useCallback(
        (newAuthType: MCPServerAuthType) => {
            const currentServerParams = form.getFieldValue(['serverInfo', 'serverParams']) || [];

            if (newAuthType === 'CLOUD_INIT_IAM') {
                const filteredParams = currentServerParams.filter((param: any) => !param.isSystemVar);
                form.setFieldValue(['serverInfo', 'serverParams'], filteredParams);
            } else {
                const filteredParams = currentServerParams.filter((param: any) => !param.isSystemVar);
                form.setFieldValue(['serverInfo', 'serverParams'], filteredParams);
            }
        },
        [form]
    );

    return (
        // style={{display: hidden ? 'none' : 'block'}}
        <div>
            <ContentWrapper>
                <LeftContent>
                    <Form.Item name={SERVER_STATUS_PATH} hidden />
                    <ServiceNameField path={SERVER_INFO_PATH} />
                    <EnglishIdentifierField disabled path={SERVER_INFO_PATH} />
                    <MCPIdentifierField path={SERVER_INFO_PATH} />
                    <DescriptionField path={SERVER_INFO_PATH} />
                    <SceneField path={SERVER_INFO_PATH} />
                    <ProtocolField path={SERVER_INFO_PATH} disabled />
                    {serverSourceType === 'openapi' && (
                        <AuthTypeField path={SERVER_INFO_PATH} onAuthTypeChange={handleAuthTypeChange} />
                    )}
                    <Form.Item label="全局变量" name={SERVER_PARAMS_PATH}>
                        <GlobalVariableField
                            path={SERVER_PARAMS_PATH}
                            rowKey="index"
                            systemVars={systemVars}
                        />
                    </Form.Item>
                    {serverSourceType === 'openapi' && (
                        <AuthDescription path={SERVER_INFO_PATH} />
                    )}
                </LeftContent>
                <RightContent>
                    {
                        serverSourceType === 'external' && (
                            <ServerConfigFieldWidthWrapper path={SERVER_INFO_PATH} />
                        )
                    }
                    <OverviewField />
                </RightContent>
            </ContentWrapper>
        </div>
    );
};

export default BasicInfoContent;
